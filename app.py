import argparse
import logging
import sys
import threading
import time
from utils.translate import translate
from utils.positions import get_button_positions
from utils.get_translation_dictonaries import get_translation_dict
from utils.overlays import TranslationOverlay

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s: %(message)s')

#Parse arguments
parser = argparse.ArgumentParser(description='Translate words from an image.')
parser.add_argument('--mode', type=str, choices=['typing', 'multiple-choice', 'sentence'], default='multiple-choice',
                    help='The mode to run the program in. Options are "typing", "multiple-choice", and "sentence".')
parser.add_argument('--dictionary', type=str)
parser.add_argument('--overlay', action='store_true', help='Show the translation overlay.')
args = parser.parse_args()

# Load the dictionary
try:
    translation_dict = get_translation_dict(args.dictionary)
except Exception as e:
    logging.error(f"Could not load translations: {e}")
    sys.exit(1)

# Load button positions
button_positions = get_button_positions()

# Init overlay if needed
if args.overlay:
    overlay = TranslationOverlay()
    overlay_thread = threading.Thread(target=overlay.start, daemon=True)
    overlay_thread.start()



import logging
from difflib import get_close_matches
def translate(word, translation_dict, threshold=0.8):
    """
    Find translation for a Dutch word and return only the corresponding German translation.
    """
    if not word:
        logging.warning("Empty word provided")
        return None
    
    original_word = word.strip().lower()
    logging.info(f"Looking up translation for: {original_word}")
    
    # First try exact match
    if original_word in translation_dict:
        translation = translation_dict[original_word]
        logging.info(f"Exact match found for '{original_word}': {translation}")
        return translation
    
    # Try fuzzy matching if no exact match is found
    all_words = list(translation_dict.keys())
    close_matches = get_close_matches(original_word, all_words, n=1, cutoff=threshold)
    if close_matches:
        matched_word = close_matches[0]
        translation = translation_dict[matched_word]
        logging.info(f"Fuzzy match found for '{original_word}' (matched with '{matched_word}'): {translation}")
        return translation

    logging.warning(f"No match found for '{original_word}'")
    return None
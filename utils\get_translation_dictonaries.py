import csv
import logging

def get_translation_dict(filename):
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            reader = csv.reader(file, delimiter=',')
            dict = {row[0].lower(): row[1].lower() for row in reader if len(row) >= 2}
        logging.info(f"Loaded {len(dict)} translations from {filename}")
        dict_mirror = {v: k for k, v in dict.items()}
        return dict, dict_mirror
    except FileNotFoundError:
        logging.error(f"Translation file {filename} not found!")
        return {}
    except Exception as e:
        logging.error(f"Error loading translations: {e}")
        return {}
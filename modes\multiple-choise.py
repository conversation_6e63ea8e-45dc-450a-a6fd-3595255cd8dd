from utils.config import load_config
import logging
from utils.translate import translate

def main():
    config = load_config()
    regions = config["screen_regions"]
    try: 
            # Extract the target word
            word_image = take_screenshot(regions['word_region'])
            word_image.save('word.png')
            target_word = extract_text(word_image, debug=True, custom_config=True)
            logging.info(f"Target Word: {target_word}")

            # Find translation
            translation = translate(target_word, translation_dict)
            if not translation:
                logging.error(f"No translation found for: {target_word}")

            logging.info(f"Translation: {translation}")

            # Extract options
            options = extract_options(regions['options_region'])
            logging.info(f"Options: {options}")

            # Find and click the correct option
            clicked = click_option(translation, regions['options_region'], options)
            
            if not clicked:
                logging.error("Failed to click the correct option")
                break
            sleep(1)
        except Exception as e:
            logging.error(f"Main process error: {e}")
import webview
import pya<PERSON>gu<PERSON> as pg
from time import sleep
import threading

def take_screenshot_and_lock_mouse(window_title, width, height, filename="webview_screenshot.png"):
    sleep(8)  # Wait for the window to be fully loaded and visible
    try:
        window = pg.getWindowsWithTitle(window_title)[0]
        if window:
            window.activate()
            sleep(0.5)
            x, y = window.topleft
            # Move mouse to center of the window
            center_x = x + width // 2
            center_y = y + height // 2
            pg.moveTo(center_x, center_y)
            # Lock mouse inside window
            def mouse_lock_loop():
                while True:
                    mx, my = pg.position()
                    if not (x <= mx <= x + width and y <= my <= y + height):
                        pg.moveTo(center_x, center_y)
                    sleep(0.05)
            lock_thread = threading.Thread(target=mouse_lock_loop, daemon=True)
            lock_thread.start()
            # Take screenshot of the exact window size
            screenshot = pg.screenshot(region=(x, y, width, height))
            screenshot.save(filename)
            print(f"Screenshot saved as {filename}")
        else:
            print(f"Window with title '{window_title}' not found.")
    except IndexError:
        print(f"No window found with the title '{window_title}'.")
    except Exception as e:
        print(f"An error occurred: {e}")

def main():
    # Start screenshot and mouse lock thread
    screenshot_thread = threading.Thread(
        target=take_screenshot_and_lock_mouse,
        args=("AutoSlimstampen", 800, 600, "autoslimstampen_screenshot.png"),
        daemon=True
    )
    screenshot_thread.start()

    # Run webview in the main thread
    webview.create_window(
        "AutoSlimstampen",
        "https://isw.magister.net",
        width=800,
        height=600,
        resizable=False
    )
    webview.start()

if __name__ == "__main__":
    main()
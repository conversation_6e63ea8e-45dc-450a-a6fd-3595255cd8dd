import json

def load_config(file):
    try:
        with open(file, 'r', encoding='utf-8') as config_file:
            config = json.load(config_file)
        return config
    except FileNotFoundError:
        print("Configuration file config.json not found!")
        return {}
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON from config.json: {e}")
        return {}
    except Exception as e:
        print(f"Unexpected error loading configuration: {e}")
        return {}

import csv
import pyautogui as pg
import pytesseract
import numpy as np
from time import sleep
import tkinter as tk
import logging
import sys 
import sys
import re
import threading
from PIL import Image
from difflib import get_close_matches
import utils.positions as positions

#configure utf-8 encoding
sys.stdout.reconfigure(encoding='utf-8')

class TranslationOverlay:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Translation Overlay")
        self.root.geometry("300x200")
        self.root.attributes('-topmost', True)
        self.root.configure(bg='white')
        
        # Make window semi-transparent
        self.root.attributes('-alpha', 0.8)
        
        # Create main frame
        self.main_frame = tk.Frame(self.root, bg='white')
        self.main_frame.pack(expand=True, fill='both', padx=10, pady=10)
        
        # Create labels for word and translation
        self.word_label = tk.Label(
            self.main_frame,
            text="Word:",
            font=("Arial", 12, "bold"),
            bg='white'
        )
        self.word_label.pack(expand=True)

        self.translation_label = tk.Label(
            self.main_frame,
            text="Translation:",
            font=("Arial", 12, "bold"),
            bg='white'
        )
        self.translation_label.pack(expand=True)

        self.exit_button = tk.Button(
            self.main_frame,
            text="Exit",
            command=self.root.quit,
            font=("Arial", 12),
            bg='red',
            fg='white'
        )
        self.exit_button.pack(expand=True)
        
        # Keep window on top
        self.root.lift()
        self.root.attributes('-topmost', True)

    def update_display(self, word, translation):
        """Update the display with new word and translation"""
        self.word_label.config(text=f"Word: {word}")
        self.translation_label.config(text=f"Translation: {translation}")
        self.root.update()

    def start(self):
        """Start the overlay window"""
        self.root.mainloop()

    def run_translation_loop(self):
        """Run the main translation loop"""
        regions = detect_screen_regions()
        while True:
            try:
                word_image = take_screenshot(regions['word_region'])
                if word_image:
                    #word_image.save("word.png")
                    #image = Image.open("word.png")
                    text = extract_text(word_image, debug=True)
                    translation = find_translation(text, data)
                    if translation:
                        self.update_display(translation[0], translation[1])
                else:
                    logging.warning("Skipping iteration due to failed screenshot.")
                sleep(3)
            except Exception as e:
                logging.error(f"Translation loop error: {e}")


# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s: %(message)s')

# Load CSV file with error handling
def load_translation_dict(filename):
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            # Handle potential empty or malformed CSV
            data = {row[0].lower(): row[1].lower() for row in reader if len(row) >= 2}
        logging.info(f"Loaded {len(data)} translations from {filename}")
        return data
    except FileNotFoundError:
        logging.error(f"Translation file {filename} not found!")
        return {}
    except Exception as e:
        logging.error(f"Error loading translations: {e}")
        return {}

# Load CSV file
try:
    data = load_translation_dict('words.csv')
except Exception as e:
    logging.error(f"Could not load translations: {e}")
    sys.exit(1)
    
button_positions = positions.get_button_positions()

# Set up Tesseract path (with error handling)
try:
    pytesseract.pytesseract.tesseract_cmd = r'C:/Program Files/Tesseract-OCR/tesseract.exe'
except Exception as e:
    logging.error(f"Tesseract setup error: {e}")
    sys.exit(1)

def find_translation(word, data, threshold=0.8):
    """
    Find the translation for a given word with robust matching.

    Args:
        word (str): The word to translate.
        data (dict): The dictionary containing word translations.
        threshold (float): Similarity threshold for close matches (0 to 1).

    Returns:
        tuple: (matched_word, translation) if found, or (None, None) if no match exists.
    """
    if not word:
        logging.warning("Empty word provided")
        return None, None

    # Normalize input
    word = word.strip().lower()
    normalized_data = {key.strip().lower(): value for key, value in data.items()}

    # Try exact match first
    if word in normalized_data:
        translation = normalized_data[word]
        logging.info(f"Exact match found for '{word}': {translation}")
        return word, translation

    # If no exact match, try partial matching
    for key in normalized_data:
        if key in word or word in key:
            translation = normalized_data[key]
            logging.info(f"Partial match found for '{word}': {translation} (matched with '{key}')")
            return key, translation

    # Fuzzy matching using difflib
    close_matches = get_close_matches(word, normalized_data.keys(), n=1, cutoff=threshold)
    if close_matches:
        close_match = close_matches[0]
        translation = normalized_data[close_match]
        logging.info(f"Fuzzy match found for '{word}' (matched with '{close_match}'): {translation}")
        return close_match, translation

    # No match found
    logging.warning(f"No match found for '{word}'")
    return None, None
    
def take_screenshot(region=None):
    """Capture a screenshot with error handling."""
    try:
        screenshot = pg.screenshot(region=region)
        if screenshot is None:
            logging.error("Screenshot failed: returned None")
        return screenshot
    except Exception as e:
        logging.error(f"Screenshot capture error: {e}")
        return None

def extract_text(image, debug=False, custom_config=None):
    """
    Enhanced text extraction with multiple preprocessing techniques.
    Handles unexpected characters gracefully and improves logging.
    """
    if image is None:
        logging.error("No image provided for OCR")
        return ""
    
    def clean_text(text):
        """
        Filter out unsupported characters and return sanitized text.
        Keeps accented letters, punctuation, and common symbols.
        """
        allowed_characters = re.compile(r"[a-zA-Z0-9éèêëàâäôöûüùïîçÉÈÊËÀÂÄÔÖÛÜÙÏÎÇ.,!?\'\"()\[\]{}<>/\\|+=*&^%$#@;:\-_~`]+")
        sanitized_text = " ".join(allowed_characters.findall(text))
        logging.info(f"Cleaned Text: {sanitized_text}")
        return sanitized_text


    try:
        # Convert PIL Image to NumPy array
        image_np = np.array(image)

        # Use default or provided Tesseract config
        config = custom_config or '--psm 6'
        text = pytesseract.image_to_string(image_np, lang='eng', config=config).strip()

        # Clean the extracted text
        sanitized_text = clean_text(text)

        if debug:
            logging.info(f"Extracted Text (raw): {text}")
            logging.info(f"Sanitized Text: {sanitized_text}")

        return sanitized_text if sanitized_text else ""

    except Exception as e:
        logging.error(f"Text extraction failed. Error: {e}")
        logging.debug("Exception details:", exc_info=True)
        return ""
    
# Screen regions (add runtime detection or configuration)
def detect_screen_regions():
    """Placeholder for screen region detection logic."""
    logging.warning("Using hardcoded screen regions. Consider implementing auto-detection.")
    return {
        'word_region': (761, 273, 438, 45),   # Word region       
        'options_region': (704, 356, 182, 235)  # Options region
    }

def main():
    overlay = TranslationOverlay()
    # Run the translation loop in a separate thread to keep UI responsive
    translation_thread = threading.Thread(target=overlay.run_translation_loop, daemon=True)
    translation_thread.start()
    # Start the overlay window
    overlay.start()

if __name__ == "__main__":
    main()
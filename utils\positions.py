import pyautogui as pg

def get_button_positions():
    screenWidth, screenHeight = pg.size()

    print(screenWidth, screenHeight)

    button_3 = screenWidth / 2, screenHeight / 2 + 50
    button_2 = screenWidth / 2, screenHeight / 2 - 20
    button_1 = screenWidth / 2, screenHeight / 2 - 90
    button_0 = screenWidth / 2, screenHeight / 2 - 155 

    return {
        'pos_0': button_0,
        'pos_1': button_1,
        'pos_2': button_2,
        'pos_3': button_3
    }

def get_word_position():
    screenWidth, screenHeight = pg.size()
    
    word_position_1 = screenWidth / 2 + 200, screenHeight / 2 - 220
    word_position_2 = screenWidth / 2 - 200, screenHeight / 2 - 280

    region = (word_position_1[0], word_position_1[1], word_position_2[0], word_position_2[1])
    region = tuple(map(int, region))
    return region
import tkinter as tk

class TranslationOverlay:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Translation Overlay")
        self.root.geometry("300x200")
        self.root.attributes('-topmost', True)
        self.root.configure(bg='white')
        
        # Make window semi-transparent
        self.root.attributes('-alpha', 0.8)
        
        # Create main frame
        self.main_frame = tk.Frame(self.root, bg='white')
        self.main_frame.pack(expand=True, fill='both', padx=10, pady=10)
        
        # Create labels for word and translation
        self.word_label = tk.Label(
            self.main_frame,
            text="Word:",
            font=("Arial", 12, "bold"),
            bg='white'
        )
        self.word_label.pack(expand=True)

        self.translation_label = tk.Label(
            self.main_frame,
            text="Translation:",
            font=("Arial", 12, "bold"),
            bg='white'
        )
        self.translation_label.pack(expand=True)

        self.exit_button = tk.Button(
            self.main_frame,
            text="Exit",
            command=self.root.quit,
            font=("Arial", 12),
            bg='red',
            fg='white'
        )
        self.exit_button.pack(expand=True)
        
        # Keep window on top
        self.root.lift()
        self.root.attributes('-topmost', True)

    def update_display(self, word, translation):
        """Update the display with new word and translation"""
        self.word_label.config(text=f"Word: {word}")
        self.translation_label.config(text=f"Translation: {translation}")
        self.root.update()

    def start(self):
        self.root.mainloop()




import csv
import pya<PERSON>gu<PERSON> as pg
from PIL import Image
import pytesseract
import numpy as np
from time import sleep
import re
from difflib import get_close_matches
import utils.positions as positions
import logging
import sys
import json

button_positions = positions.get_button_positions()

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s: %(message)s')

with open('config.json', 'r', encoding='utf-8') as config_file:
    config = json.load(config_file)
    logging.info("Configuration loaded successfully")

# Load CSV file with error handling
def load_translation_dict(filename):
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            # Handle potential empty or malformed CSV
            data = {row[0].lower(): row[1].lower() for row in reader if len(row) >= 2}
        logging.info(f"Loaded {len(data)} translations from {filename}")
        return data
    except FileNotFoundError:
        logging.error(f"Translation file {filename} not found!")
        return {}
    except Exception as e:
        logging.error(f"Error loading translations: {e}")
        return {}

# Load CSV file
try:
    data = load_translation_dict(config["dictionary"])
except Exception as e:
    logging.error(f"Could not load translations: {e}")
    sys.exit(1)

# Set up Tesseract path (with error handling)
try:
    pytesseract.pytesseract.tesseract_cmd = r'C:/Program Files/Tesseract-OCR/tesseract.exe'
except Exception as e:
    logging.error(f"Tesseract setup error: {e}")
    sys.exit(1)

def find_translation(word, translation_dict, threshold=0.8):
    """
    Find translation for a Dutch word and return only the corresponding German translation.
    """
    if not word:
        logging.warning("Empty word provided")
        return None
    
    original_word = word.strip().lower()
    logging.info(f"Looking up translation for: {original_word}")
    
    # First try exact match
    if original_word in translation_dict:
        translation = translation_dict[original_word]
        logging.info(f"Exact match found for '{original_word}': {translation}")
        return translation
    
    # Try fuzzy matching if no exact match is found
    all_words = list(translation_dict.keys())
    close_matches = get_close_matches(original_word, all_words, n=1, cutoff=threshold)
    if close_matches:
        matched_word = close_matches[0]
        translation = translation_dict[matched_word]
        logging.info(f"Fuzzy match found for '{original_word}' (matched with '{matched_word}'): {translation}")
        return translation

    logging.warning(f"No match found for '{original_word}'")
    return None

def take_screenshot(region=None):
    """Capture a screenshot with error handling."""
    try:
        return pg.screenshot(region=region)
    except Exception as e:
        logging.error(f"Screenshot capture error: {e}")
        return None

def extract_text(image, debug=False, custom_config=None):
    """Enhanced text extraction with multiple preprocessing techniques."""
    if image is None:
        logging.error("No image provided for OCR")
        return ""
        
    try:
        # Convert PIL Image to NumPy array
        image_np = np.array(image)
    
        text = pytesseract.image_to_string(image_np, lang='eng').strip()
        return text if text else ""
    
    except Exception as e:
        logging.error(f"Text extraction error: {e}")
        return ""
    

def extract_options(region):
    """Extract text from options with error handling."""
    screenshot = take_screenshot(region)
    screenshot.save('options.png')
    if screenshot is None:
        return []
    
    text = extract_text(screenshot, debug=True, custom_config=True)
    options = [re.sub(r'[^a-zA-Z\s]', '', opt.strip()) for opt in text.split("\n") if opt.strip()]
    logging.info(f"Extracted {len(options)} options")
    return options

def click_option(translation, region, options):
    if not translation:
        logging.warning("No translation provided")
        return False
    if  not options:
        logging.warning("No options provided")
        return False
    
    if translation not in options:
        logging.warning(f"Translation '{translation}' not found in options")
        logging.info("Trying to find closest match")
        closest_match = get_close_matches(translation, options, n=1)
        if closest_match:
            translation = closest_match[0]
            logging.info(f"Closest match found: {translation}")     
        else:
            logging.error("No close match found")
            return False
    
    for a in options:
        if a == translation:
            logging.info(f"Option is the {options.index(a)} button")
            pg.moveTo(button_positions[f'pos_{options.index(a)}'])
            pg.click(button='left')
            pg.press('enter')
            sleep(0.3)
            pg.press('enter')
            return True


def detect_screen_regions():
    """Placeholder for screen region detection logic."""
    logging.warning("Using hardcoded screen regions. Consider implementing auto-detection.")
    return {
        'word_region': (665, 301, 585, 58),   # Word region       
        'options_region': (572, 352, 797, 306)  # Options region
    }

def main():
    while True:
        try:
            # Detect screen regions
            regions = detect_screen_regions()
            
            # Extract the target word
            word_image = take_screenshot(regions['word_region'])
            word_image.save('word.png')
            target_word = extract_text(word_image, debug=True, custom_config=True)
            logging.info(f"Target Word: {target_word}")

            # Find translation
            translation = find_translation(target_word, data)
            if not translation:
                logging.error(f"No translation found for: {target_word}")

            logging.info(f"Translation: {translation}")

            # Extract options
            options = extract_options(regions['options_region'])
            logging.info(f"Options: {options}")

            # Find and click the correct option
            clicked = click_option(translation, regions['options_region'], options)
            
            if not clicked:
                logging.error("Failed to click the correct option")
                break
            sleep(1)
        except Exception as e:
            logging.error(f"Main process error: {e}")

if __name__ == "__main__":
    main()